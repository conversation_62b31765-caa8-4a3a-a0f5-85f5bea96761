import { Link } from "react-router-dom";
import { useContext } from "react";
import { AuthContext } from "../contexts/AuthContext";
import { getProfileImageSrc } from "../utils/imageUtils";

export default function Navbar() {
  const { user, logout } = useContext(AuthContext);

  const handleLogout = () => {
    logout();
  };

  return (
    <nav className="bg-white shadow-md px-4 py-3">
      <div className="max-w-6xl mx-auto flex justify-between items-center">
        <Link
          to={user ? "/feed" : "/"}
          className="text-xl font-bold text-blue-600 hover:text-blue-700"
        >
          DevConnect
        </Link>

        <div className="flex items-center space-x-4">
          {user ? (
            <>
              <span className="text-blue-600">Welcome, {user.username}</span>

              <Link
                to="/feed"
                className="text-gray-700 hover:text-blue-600 transition-colors"
              >
                Feed
              </Link>

              <Link
                to={`/profile/${user.username}`}
                className="flex items-center space-x-2 text-gray-700 hover:text-blue-600 transition-colors"
              >
                <span>Profile</span>
                <img
                  src={getProfileImageSrc(user.profilepic)}
                  alt={`${user.username}'s avatar`}
                  className="w-8 h-8 rounded-full object-cover"
                  onError={(e) => {
                    e.target.src = "/defaultAvatar.svg";
                  }}
                />
              </Link>

              <button
                onClick={handleLogout}
                className="bg-red-500 text-white px-3 py-1 rounded hover:bg-red-600 transition-colors"
              >
                Logout
              </button>
            </>
          ) : (
            <>
              <Link
                to="/"
                className="text-gray-700 hover:text-blue-600 transition-colors"
              >
                Login
              </Link>
              <Link
                to="/register"
                className="bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 transition-colors"
              >
                Register
              </Link>
            </>
          )}
        </div>
      </div>
    </nav>
  );
}

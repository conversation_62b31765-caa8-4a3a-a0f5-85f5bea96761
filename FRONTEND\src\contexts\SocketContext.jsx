import { createContext, useState, useEffect, useRef, Children } from "react";
import {io} from "socket.io-client"

const socketContext = createContext();

export const SocketProvider = ({children}) =>{

    const socket = useRef();

    useEffect(()=>{
        socket.current = io("http://localhost:3000");
        return () => socket.current.disconnect();
    }, []);


    return(
        <socketContext.Provider value={socket.current}>
            {children}
        </socketContext.Provider>
    )
};

export const useSocket = () => useContext(socketContext);
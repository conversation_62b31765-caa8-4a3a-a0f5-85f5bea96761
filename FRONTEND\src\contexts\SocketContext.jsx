import { createContext, useEffect, useRef, useContext } from "react";
import { io } from "socket.io-client";

const socketContext = createContext();

export const SocketProvider = ({ children }) => {
    const socket = useRef();

    useEffect(() => {
        // Initialize socket connection
        socket.current = io("http://localhost:3000", {
            autoConnect: true,
            reconnection: true,
            reconnectionDelay: 1000,
            reconnectionAttempts: 5,
            timeout: 20000,
        });

        // Connection event handlers
        socket.current.on("connect", () => {
            console.log("Socket connected:", socket.current.id);
        });

        socket.current.on("disconnect", (reason) => {
            console.log("Socket disconnected:", reason);
        });

        socket.current.on("connect_error", (error) => {
            console.error("Socket connection error:", error);
        });

        // Cleanup on unmount
        return () => {
            if (socket.current) {
                socket.current.disconnect();
            }
        };
    }, []);

    return (
        <socketContext.Provider value={socket.current}>
            {children}
        </socketContext.Provider>
    );
};

export const useSocket = () => {
    const context = useContext(socketContext);
    if (!context) {
        throw new Error("useSocket must be used within a SocketProvider");
    }
    return context;
};
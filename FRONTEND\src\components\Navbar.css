/* Navbar animations and custom styles */

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideDown {
  animation: slideDown 0.2s ease-out;
}

/* Custom hover effects for navbar items */
.navbar-item {
  position: relative;
  overflow: hidden;
}

.navbar-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s;
}

.navbar-item:hover::before {
  left: 100%;
}

/* Smooth transitions for all interactive elements */
.navbar-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom gradient backgrounds */
.navbar-gradient-blue {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
}

.navbar-gradient-red {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

/* Mobile menu backdrop blur effect */
.mobile-menu-backdrop {
  backdrop-filter: blur(8px);
  background-color: rgba(255, 255, 255, 0.95);
}

/* Logo pulse animation on hover */
.logo-pulse:hover {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Profile picture border animation */
.profile-pic-border {
  position: relative;
}

.profile-pic-border::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #3b82f6, #8b5cf6, #3b82f6);
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.profile-pic-border:hover::after {
  opacity: 1;
}

/* Notification badge styles */
.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  border: 2px solid white;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Responsive text scaling */
@media (max-width: 640px) {
  .navbar-text-responsive {
    font-size: 0.875rem;
  }
}

@media (max-width: 480px) {
  .navbar-text-responsive {
    font-size: 0.75rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .navbar-dark {
    background-color: #1f2937;
    border-color: #374151;
  }
  
  .navbar-dark .text-gray-700 {
    color: #d1d5db;
  }
  
  .navbar-dark .text-gray-600 {
    color: #9ca3af;
  }
}
